name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, frontend-rewrite-v3 ]
  pull_request:
    branches: [ main, develop, frontend-rewrite-v3 ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

permissions:
  contents: read
  security-events: write
  actions: read

env:
  GO_VERSION: '1.23'
  NODE_VERSION: '18'
  GOLANGCI_LINT_VERSION: 'v1.64.8'

jobs:
  # Code quality and linting
  lint:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Clear potential cache conflicts
        run: |
          # Clear any potential cache conflicts by removing old cache entries
          echo "Clearing potential cache conflicts for run ${{ github.run_id }}"

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-lint-${{ hashFiles('**/go.sum') }}-${{ github.run_id }}
          restore-keys: |
            ${{ runner.os }}-go-lint-${{ hashFiles('**/go.sum') }}-
            ${{ runner.os }}-go-lint-
            ${{ runner.os }}-go-

      - name: Install golangci-lint
        run: |
          curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin ${{ env.GOLANGCI_LINT_VERSION }}

      - name: Run golangci-lint
        run: golangci-lint run --timeout=5m

      - name: Check Go formatting
        run: |
          if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
            echo "The following files are not formatted:"
            gofmt -s -l .
            exit 1
          fi

      - name: Check Go modules
        run: |
          go mod tidy
          if [ -n "$(git status --porcelain go.mod go.sum)" ]; then
            echo "go.mod or go.sum is not up to date"
            git diff go.mod go.sum
            exit 1
          fi

  # Unit tests
  test:
    name: Unit Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-test-${{ hashFiles('**/go.sum') }}-${{ github.run_id }}
          restore-keys: |
            ${{ runner.os }}-go-test-${{ hashFiles('**/go.sum') }}-
            ${{ runner.os }}-go-test-
            ${{ runner.os }}-go-

      - name: Install dependencies
        run: go mod download

      - name: Install additional testing tools
        run: |
          # Install golangci-lint for static analysis (same version as lint job)
          curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin ${{ env.GOLANGCI_LINT_VERSION }}
          # Install staticcheck for advanced static analysis
          go install honnef.co/go/tools/cmd/staticcheck@latest
          # Install goleak for goroutine leak detection
          go get -t go.uber.org/goleak

      - name: Run static analysis for concurrency issues
        run: |
          echo "Running static analysis for concurrency issues..."
          # Check for common concurrency issues
          go vet -composites=false ./...
          # Run staticcheck for advanced concurrency analysis
          staticcheck ./...
          # Run golangci-lint with concurrency-focused linters on test files specifically (matching .golangci.yml config)
          golangci-lint run --skip-files="" --tests=true ./...

      - name: Build test server binary for CI
        run: |
          go build -o test-server-ci ./cmd/server
          chmod +x test-server-ci

      - name: Run unit tests with stress testing
        env:
          NO_AUTO_ANALYZE: "true"
          NO_DOCKER: "true"
          LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
          LLM_API_KEY_SECONDARY: ${{ secrets.LLM_API_KEY_SECONDARY || '' }}
          LLM_BASE_URL: ${{ secrets.LLM_BASE_URL || '' }}
        run: |
          echo "Running unit tests with concurrency stress testing..."
          # Run tests multiple times to catch race conditions
          echo "Running tests 3 times to catch intermittent issues..."
          go test -v -count=1 -parallel=4 -coverprofile=coverage-1.out -covermode=atomic ./... || exit 1
          echo "Test run 1/3 completed"
          go test -v -count=1 -parallel=4 -coverprofile=coverage-2.out -covermode=atomic ./... || exit 1
          echo "Test run 2/3 completed"
          go test -v -count=1 -parallel=4 -coverprofile=coverage-3.out -covermode=atomic ./... || exit 1
          echo "Test run 3/3 completed"
          # Use the last coverage file
          mv coverage-3.out coverage.out
          rm -f coverage-*.out

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage.out
          flags: unittests
          name: codecov-umbrella

  # Integration tests
  integration:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [lint, test]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-integration-${{ hashFiles('**/go.sum') }}-${{ github.run_id }}
          restore-keys: |
            ${{ runner.os }}-go-integration-${{ hashFiles('**/go.sum') }}-
            ${{ runner.os }}-go-integration-
            ${{ runner.os }}-go-

      - name: Cache Node modules
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-integration-${{ hashFiles('**/package-lock.json') }}-${{ github.run_id }}
          restore-keys: |
            ${{ runner.os }}-node-integration-${{ hashFiles('**/package-lock.json') }}-
            ${{ runner.os }}-node-integration-
            ${{ runner.os }}-node-

      - name: Install Node dependencies
        run: npm ci

      - name: Install Newman
        run: npm install -g newman

      - name: Build application
        run: go build -o newsbalancer ./cmd/server

      - name: Start application
        env:
          NO_AUTO_ANALYZE: "true"
          PORT: 8080
          LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
          LLM_API_KEY_SECONDARY: ${{ secrets.LLM_API_KEY_SECONDARY || '' }}
          LLM_BASE_URL: ${{ secrets.LLM_BASE_URL || '' }}
        run: |
          ./newsbalancer &
          APP_PID=$!
          echo "APP_PID=$APP_PID" >> $GITHUB_ENV
          
          # Wait for application to start
          for i in {1..30}; do
            if curl -s http://localhost:8080/api/articles > /dev/null; then
              echo "Application started successfully"
              break
            fi
            echo "Waiting for application to start... ($i/30)"
            sleep 2
          done

      - name: Run Newman tests
        run: |
          newman run postman/unified_backend_tests.json \
            --environment postman/newman_environment.json \
            --globals postman/NewsBalancer.postman_globals.json \
            --reporters cli,json \
            --reporter-json-export newman-results.json

      - name: Upload Newman results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: newman-results
          path: newman-results.json

      - name: Stop application
        if: always()
        run: |
          if [ ! -z "$APP_PID" ]; then
            kill $APP_PID || true
          fi

  # Security scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Set up Go for gosec
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Install and run gosec
        run: |
          go install github.com/securego/gosec/v2/cmd/gosec@latest
          gosec -fmt sarif -out gosec-results.sarif ./... || true
          # Ensure the file exists even if no issues found
          if [ ! -f gosec-results.sarif ]; then
            echo '{"version":"2.1.0","$schema":"https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json","runs":[{"tool":{"driver":{"name":"gosec","version":"dev"}},"results":[]}]}' > gosec-results.sarif
          fi
          ls -la gosec-results.sarif

      - name: Upload gosec scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'gosec-results.sarif'

  # Build and test Docker image
  docker:
    name: Docker Build & Test
    runs-on: ubuntu-latest
    needs: [lint, test]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.app
          target: debug
          push: false
          load: true
          tags: newsbalancer:test
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Test Docker image
        run: |
          # Skip inspection commands as they trigger the application due to ENTRYPOINT
          echo "=== Starting Docker container tests ==="

          # Test the health check flag first
          echo "=== Testing health check flag ==="
          if docker run --rm \
            -e TEST_MODE=true \
            -e DOCKER=true \
            newsbalancer:test --health-check; then
            echo "Health check passed - binary is working"
          else
            echo "Health check failed - binary has issues"
            exit 1
          fi

          # Try to run the application and capture any startup errors
          echo "=== Testing application startup ==="
          docker run --rm --name newsbalancer-test -p 8080:8080 \
            -e PORT=8080 \
            -e TEST_MODE=true \
            -e DOCKER=true \
            -e DB_CONNECTION=":memory:" \
            -e NO_AUTO_ANALYZE=true \
            -e LLM_API_KEY="${{ secrets.LLM_API_KEY || 'test-key-for-docker' }}" \
            -e LLM_API_KEY_SECONDARY="${{ secrets.LLM_API_KEY_SECONDARY || '' }}" \
            -e LLM_BASE_URL="${{ secrets.LLM_BASE_URL || '' }}" \
            newsbalancer:test &

          CONTAINER_PID=$!
          sleep 10

          # Check if container is still running
          if ! docker ps | grep newsbalancer-test; then
            echo "Container exited unexpectedly. Checking logs:"
            docker logs newsbalancer-test 2>&1 || echo "No logs available"

            # Try running with explicit error output
            echo "=== Attempting to run with explicit error capture ==="
            docker run --rm \
              -e TEST_MODE=true \
              -e DOCKER=true \
              newsbalancer:test 2>&1 | head -20 || echo "Failed to capture startup errors"
            exit 1
          fi

          # Basic health check
          if curl -f http://localhost:8080/api/articles; then
            echo "Docker image test passed"
          else
            echo "Docker image test failed. Container logs:"
            docker logs newsbalancer-test 2>&1
            exit 1
          fi

          docker stop newsbalancer-test || true

  # Frontend Quality Assurance
  frontend-qa:
    name: Frontend Quality & Performance
    runs-on: ubuntu-latest
    needs: [lint, test]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run CSS linting
        run: npm run lint:css

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Build application
        run: go build -o newsbalancer ./cmd/server

      - name: Run Lighthouse CI
        env:
          NO_AUTO_ANALYZE: "true"
          PORT: 8080
          LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
          LLM_API_KEY_SECONDARY: ${{ secrets.LLM_API_KEY_SECONDARY || '' }}
          LLM_BASE_URL: ${{ secrets.LLM_BASE_URL || '' }}
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
        run: |
          # Install Lighthouse CI globally for better performance
          npm install -g @lhci/cli

          # Start the server in background for Lighthouse CI
          ./newsbalancer &
          SERVER_PID=$!
          echo "SERVER_PID=$SERVER_PID" >> $GITHUB_ENV

          # Wait for server to be ready
          timeout 30 bash -c 'until curl -f http://localhost:8080/articles; do sleep 1; done'

          # Run Lighthouse CI with our configuration
          lhci autorun

          # Stop the server
          kill $SERVER_PID || true

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Run accessibility tests
        env:
          NO_AUTO_ANALYZE: "true"
          PORT: 8080
          LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
          LLM_API_KEY_SECONDARY: ${{ secrets.LLM_API_KEY_SECONDARY || '' }}
          LLM_BASE_URL: ${{ secrets.LLM_BASE_URL || '' }}
          CI: true
        run: |
          # Seed test data directly into database BEFORE starting server
          echo "Seeding test data for accessibility tests..."
          export DATABASE_PATH="newsbalancer.db"
          export DB_CONNECTION="newsbalancer.db"
          go run ./cmd/seed_test_data

          # Start the server in background with same database
          export DATABASE_PATH="newsbalancer.db"
          export DB_CONNECTION="newsbalancer.db"
          ./newsbalancer &
          SERVER_PID=$!

          # Wait for server to be ready
          timeout 30 bash -c 'until curl -f http://localhost:8080/articles; do sleep 1; done'

          # Verify test data is accessible via API
          echo "Verifying test data via API..."
          curl -s http://localhost:8080/api/articles | head -200

          # Run accessibility tests
          npx playwright test tests/e2e/accessibility-pages.spec.ts --reporter=dot

          # Run progress indicator tests
          echo "Running progress indicator tests..."
          npx playwright test tests/progress-indicator.spec.ts --reporter=dot

          # Stop the server
          kill $SERVER_PID || true

  # Performance benchmarks (on main branch and frontend-rewrite-v3 for testing)
  benchmark:
    name: Performance Benchmarks
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/frontend-rewrite-v3'
    needs: [integration, docker]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Build application
        run: go build -o newsbalancer ./cmd/server

      - name: Build benchmark tool
        run: |
          cd cmd/benchmark
          go build -o benchmark .
          cd ../..

      - name: Start application
        env:
          NO_AUTO_ANALYZE: "true"
          PORT: 8080
          LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
          LLM_API_KEY_SECONDARY: ${{ secrets.LLM_API_KEY_SECONDARY || '' }}
          LLM_BASE_URL: ${{ secrets.LLM_BASE_URL || '' }}
        run: |
          ./newsbalancer &
          APP_PID=$!
          echo "APP_PID=$APP_PID" >> $GITHUB_ENV
          
          # Wait for application to start
          sleep 10

      - name: Run performance benchmarks
        run: |
          ./cmd/benchmark/benchmark \
            -test "ci-benchmark" \
            -url "http://localhost:8080" \
            -users 10 \
            -requests 50 \
            -duration "2m" \
            -output json > benchmark-results.json

      - name: Upload benchmark results
        uses: actions/upload-artifact@v4
        with:
          name: benchmark-results
          path: benchmark-results.json

      - name: Stop application
        if: always()
        run: |
          if [ ! -z "$APP_PID" ]; then
            kill $APP_PID || true
          fi

  # Deploy to staging (only on develop branch)
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    needs: [integration, security, docker]
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.CONTAINER_REGISTRY }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ${{ secrets.CONTAINER_REGISTRY }}/newsbalancer:staging-${{ github.sha }}
            ${{ secrets.CONTAINER_REGISTRY }}/newsbalancer:staging-latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Deploy to staging
        run: |
          echo "Deployment to staging would happen here"
          echo "Image: ${{ secrets.CONTAINER_REGISTRY }}/newsbalancer:staging-${{ github.sha }}"

  # Deploy to production (DISABLED - not ready for production deployment)
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    if: false  # Disabled until ready for production deployment
    needs: [integration, security, docker, benchmark]
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.CONTAINER_REGISTRY }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ${{ secrets.CONTAINER_REGISTRY }}/newsbalancer:v${{ github.run_number }}
            ${{ secrets.CONTAINER_REGISTRY }}/newsbalancer:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Deploy to production
        run: |
          echo "Deployment to production would happen here"
          echo "Image: ${{ secrets.CONTAINER_REGISTRY }}/newsbalancer:v${{ github.run_number }}"

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ github.run_number }}
          name: Release v${{ github.run_number }}
          body: |
            ## Changes in this Release
            - Automated release from commit ${{ github.sha }}
            - Docker image: ${{ secrets.CONTAINER_REGISTRY }}/newsbalancer:v${{ github.run_number }}

            ## Deployment
            This release has been automatically deployed to production.
          draft: false
          prerelease: false
